{"name": "secondbrain", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-picker/picker": "^2.11.2", "@react-navigation/bottom-tabs": "^7.4.7", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.8", "expo": "~54.0.1", "expo-clipboard": "^8.0.6", "expo-constants": "^18.0.8", "expo-device": "^8.0.6", "expo-file-system": "^19.0.11", "expo-notifications": "^0.32.10", "expo-sqlite": "^16.0.8", "expo-status-bar": "~3.0.7", "react": "19.1.0", "react-native": "0.81.4", "react-native-markdown-display": "^7.0.2", "react-native-safe-area-context": "^5.6.1", "react-native-screens": "^4.16.0", "react-native-vector-icons": "^10.3.0"}, "private": true}