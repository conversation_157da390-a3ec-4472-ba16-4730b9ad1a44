{"version": 3, "file": "AssetSources.js", "sourceRoot": "", "sources": ["../src/AssetSources.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AAEzD,OAAO,mBAAmB,MAAM,uBAAuB,CAAC;AACxD,OAAO,EAAE,YAAY,EAAE,eAAe,EAAE,MAAM,iBAAiB,CAAC;AAiBhE;;;;;GAKG;AACH,MAAM,UAAU,iBAAiB,CAAC,IAAmB;IACnD,kGAAkG;IAClG,2BAA2B;IAC3B,MAAM,KAAK,GAAG,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;IAC3E,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC;IACxD,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;IAE1F,6DAA6D;IAC7D,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;IAClF,IAAI,GAAG,EAAE,CAAC;QACR,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC;IACxC,CAAC;IAED,MAAM,SAAS,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC;IAClD,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC3E,MAAM,MAAM,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,SAAS,GAAG,aAAa,EAAE,CAAC;IAC/E,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC;QACjC,QAAQ,EAAE,QAAQ,CAAC,EAAE;QACrB,IAAI,EAAE,IAAI,CAAC,IAAI;KAChB,CAAC,CAAC;IAEH,iGAAiG;IACjG,kDAAkD;IAClD,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC;QACjD,MAAM,GAAG,GAAG,IAAI,CAAC,kBAAkB,GAAG,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC;QAC5D,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;IACvB,CAAC;IAED,4FAA4F;IAC5F,MAAM,SAAS,GAAG,YAAY,EAAE,CAAC;IAEjC,MAAM,YAAY,GAAG,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS;QACtD,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY;QACjD,CAAC,CAAC,IAAI,CAAC;IACT,IAAI,YAAY,EAAE,CAAC;QACjB,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,kBAAkB,GAAG,MAAM,EAAE,YAAY,CAAC,CAAC;QAExE,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;QAClD,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5C,OAAO;YACL,GAAG,EAAE,OAAO,CAAC,IAAI;YACjB,IAAI;SACL,CAAC;IACJ,CAAC;IAED,wDAAwD;IACxD,IAAI,aAAa,CAAC,gBAAgB,CAAC,EAAE,CAAC;QACpC,OAAO,EAAE,GAAG,EAAE,6CAA6C,kBAAkB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC;IAChG,CAAC;IAED,gGAAgG;IAChG,+FAA+F;IAC/F,kGAAkG;IAClG,4BAA4B;IAC5B,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC;AAC3B,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,UAAU,CAAC,GAAW;IACpC,yDAAyD;IACzD,OAAO,eAAe,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;AACpE,CAAC", "sourcesContent": ["import type { PackagerAsset } from '@react-native/assets-registry/registry';\nimport { Platform } from 'expo-modules-core';\nimport { PixelRatio, NativeModules } from 'react-native';\n\nimport AssetSourceResolver from './AssetSourceResolver';\nimport { getManifest2, manifestBaseUrl } from './PlatformUtils';\n\n// @docsMissing\nexport type AssetMetadata = Pick<\n  PackagerAsset,\n  'httpServerLocation' | 'name' | 'hash' | 'type' | 'scales' | 'width' | 'height'\n> & {\n  uri?: string;\n  fileHashes?: string[];\n  fileUris?: string[];\n};\n\nexport type AssetSource = {\n  uri: string;\n  hash: string;\n};\n\n/**\n * Selects the best file for the given asset (ex: choosing the best scale for images) and returns\n * a { uri, hash } pair for the specific asset file.\n *\n * If the asset isn't an image with multiple scales, the first file is selected.\n */\nexport function selectAssetSource(meta: AssetMetadata): AssetSource {\n  // This logic is based on that of AssetSourceResolver, with additional support for file hashes and\n  // explicitly provided URIs\n  const scale = AssetSourceResolver.pickScale(meta.scales, PixelRatio.get());\n  const index = meta.scales.findIndex((s) => s === scale);\n  const hash = meta.fileHashes ? (meta.fileHashes[index] ?? meta.fileHashes[0]) : meta.hash;\n\n  // Allow asset processors to directly provide the URL to load\n  const uri = meta.fileUris ? (meta.fileUris[index] ?? meta.fileUris[0]) : meta.uri;\n  if (uri) {\n    return { uri: resolveUri(uri), hash };\n  }\n\n  const fileScale = scale === 1 ? '' : `@${scale}x`;\n  const fileExtension = meta.type ? `.${encodeURIComponent(meta.type)}` : '';\n  const suffix = `/${encodeURIComponent(meta.name)}${fileScale}${fileExtension}`;\n  const params = new URLSearchParams({\n    platform: Platform.OS,\n    hash: meta.hash,\n  });\n\n  // For assets with a specified absolute URL, we use the existing origin instead of prepending the\n  // development server or production CDN URL origin\n  if (/^https?:\\/\\//.test(meta.httpServerLocation)) {\n    const uri = meta.httpServerLocation + suffix + '?' + params;\n    return { uri, hash };\n  }\n\n  // For assets during development using manifest2, we use the development server's URL origin\n  const manifest2 = getManifest2();\n\n  const devServerUrl = manifest2?.extra?.expoGo?.developer\n    ? 'http://' + manifest2.extra.expoGo.debuggerHost\n    : null;\n  if (devServerUrl) {\n    const baseUrl = new URL(meta.httpServerLocation + suffix, devServerUrl);\n\n    baseUrl.searchParams.set('platform', Platform.OS);\n    baseUrl.searchParams.set('hash', meta.hash);\n    return {\n      uri: baseUrl.href,\n      hash,\n    };\n  }\n\n  // Temporary fallback for loading assets in Expo Go home\n  if (NativeModules['ExponentKernel']) {\n    return { uri: `https://classic-assets.eascdn.net/~assets/${encodeURIComponent(hash)}`, hash };\n  }\n\n  // In correctly configured apps, we arrive here if the asset is locally available on disk due to\n  // being managed by expo-updates, and `getLocalAssetUri(hash)` must return a local URI for this\n  // hash. Since the asset is local, we don't have a remote URL and specify an invalid URL (an empty\n  // string) as a placeholder.\n  return { uri: '', hash };\n}\n\n/**\n * Resolves the given URI to an absolute URI. If the given URI is already an absolute URI, it is\n * simply returned. Otherwise, if it is a relative URI, it is resolved relative to the manifest's\n * base URI.\n */\nexport function resolveUri(uri: string): string {\n  // `manifestBaseUrl` is always an absolute URL or `null`.\n  return manifestBaseUrl ? new URL(uri, manifestBaseUrl).href : uri;\n}\n"]}