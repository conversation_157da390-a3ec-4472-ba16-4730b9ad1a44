{"name": "expo-asset", "version": "12.0.8", "description": "An Expo universal module to download assets and pass them into other APIs", "main": "build/index.js", "types": "build/index.d.ts", "sideEffects": ["*.fx.js", "*.fx.web.js"], "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "test:rsc": "jest --config jest-rsc.config.js", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "asset"], "exports": {"./app.plugin": "./app.plugin.js", "./app.plugin.js": "./app.plugin.js", "./tools/hashAssetFiles": "./tools/hashAssetFiles.js", "./package.json": "./package.json", ".": {"react-server": "./build/index.server.js", "default": "./build/index.js"}}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-asset"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/asset/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {"@expo/image-utils": "^0.8.7", "expo-constants": "~18.0.8"}, "devDependencies": {"@testing-library/react-native": "^13.2.0", "@types/node": "^22.14.0", "@types/react-native__assets": "^1.0.3", "expo-module-scripts": "^5.0.7"}, "peerDependencies": {"expo": "*", "react": "*", "react-native": "*"}, "gitHead": "fa290a5f502ef415f4392b28db9498378154384f"}