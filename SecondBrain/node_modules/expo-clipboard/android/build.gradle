plugins {
  id 'com.android.library'
  id 'expo-module-gradle-plugin'
}

group = 'host.exp.exponent'
version = '8.0.6'

android {
  namespace "expo.modules.clipboard"
  defaultConfig {
    versionCode 3
    versionName '8.0.6'
  }
}

dependencies {
  implementation "androidx.core:core-ktx:1.6.0"
  implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.5.1")
  implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.2")

  if (project.findProject(':expo-modules-test-core')) {
    testImplementation project(':expo-modules-test-core')
  }
}
