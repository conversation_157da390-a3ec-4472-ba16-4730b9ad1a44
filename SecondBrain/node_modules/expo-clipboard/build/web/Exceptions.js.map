{"version": 3, "file": "Exceptions.js", "sourceRoot": "", "sources": ["../../src/web/Exceptions.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAE/C,MAAM,OAAO,6BAA8B,SAAQ,UAAU;IAC3D;QACE,KAAK,CAAC,2BAA2B,EAAE,2DAA2D,CAAC,CAAC;IAClG,CAAC;CACF;AAED,MAAM,OAAO,oBAAqB,SAAQ,UAAU;IAClD,YAAY,KAAa;QACvB,KAAK,CAAC,kBAAkB,EAAE,gCAAgC,KAAK,EAAE,CAAC,CAAC;IACrE,CAAC;CACF;AAED,MAAM,OAAO,qBAAsB,SAAQ,UAAU;IACnD,YAAY,KAAa;QACvB,KAAK,CAAC,kBAAkB,EAAE,mCAAmC,KAAK,EAAE,CAAC,CAAC;IACxE,CAAC;CACF;AAED,MAAM,OAAO,qBAAsB,SAAQ,UAAU;IACnD;QACE,KAAK,CAAC,mBAAmB,EAAE,4CAA4C,CAAC,CAAC;IAC3E,CAAC;CACF", "sourcesContent": ["import { CodedError } from 'expo-modules-core';\n\nexport class ClipboardUnavailableException extends CodedError {\n  constructor() {\n    super('ERR_CLIPBOARD_UNAVAILABLE', \"The 'AsyncClipboard' API is not available on this browser\");\n  }\n}\n\nexport class CopyFailureException extends CodedError {\n  constructor(cause: string) {\n    super('ERR_COPY_FAILURE', `Failed to copy to clipboard: ${cause}`);\n  }\n}\n\nexport class PasteFailureException extends CodedError {\n  constructor(cause: string) {\n    super('ERR_COPY_FAILURE', `Failed to paste from clipboard: ${cause}`);\n  }\n}\n\nexport class NoPermissionException extends CodedError {\n  constructor() {\n    super('ERR_NO_PERMISSION', 'User denied permission to access clipboard');\n  }\n}\n"]}