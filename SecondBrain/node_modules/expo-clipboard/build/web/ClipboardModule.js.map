{"version": 3, "file": "ClipboardModule.js", "sourceRoot": "", "sources": ["../../src/web/ClipboardModule.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,6BAA6B,EAC7B,oBAAoB,EACpB,qBAAqB,EACrB,qBAAqB,GACtB,MAAM,cAAc,CAAC;AACtB,OAAO,EACL,YAAY,EACZ,iBAAiB,EACjB,wBAAwB,EACxB,yBAAyB,EACzB,yBAAyB,EACzB,eAAe,EACf,gCAAgC,GACjC,MAAM,SAAS,CAAC;AACjB,OAAO,EAKL,YAAY,GACb,MAAM,oBAAoB,CAAC;AAE5B,eAAe;IACb,KAAK,CAAC,cAAc,CAAC,OAAyB;QAC5C,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;YACzB,MAAM,IAAI,6BAA6B,EAAE,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC;YACH,QAAQ,OAAO,CAAC,eAAe,EAAE,CAAC;gBAChC,KAAK,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;oBACvB,yBAAyB;oBACzB,MAAM,cAAc,GAAG,MAAM,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;oBACxD,MAAM,IAAI,GAAG,MAAM,wBAAwB,CAAC,cAAc,CAAC,CAAC;oBAC5D,IAAI,CAAC,IAAI,EAAE,CAAC;wBACV,0BAA0B;wBAC1B,OAAO,MAAM,SAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;oBAC9C,CAAC;oBACD,OAAO,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;gBACzC,CAAC;gBACD,OAAO,CAAC,CAAC,CAAC;oBACR,IAAI,IAAI,GAAG,MAAM,SAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;oBAChD,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,EAAE,EAAE,CAAC;wBACzB,oDAAoD;wBACpD,MAAM,cAAc,GAAG,MAAM,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;wBACxD,MAAM,IAAI,GAAG,MAAM,wBAAwB,CAAC,cAAc,CAAC,CAAC;wBAC5D,MAAM,QAAQ,GAAG,MAAM,IAAI,EAAE,IAAI,EAAE,CAAC;wBACpC,IAAI,GAAG,eAAe,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC;oBACzC,CAAC;oBACD,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,gDAAgD;YAChD,IACE,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,EAAE,IAAI,KAAK,iBAAiB,CAAC;gBAChE,CAAC,MAAM,gCAAgC,EAAE,CAAC,EAC1C,CAAC;gBACD,MAAM,IAAI,qBAAqB,EAAE,CAAC;YACpC,CAAC;YAED,IAAI,CAAC;gBACH,oBAAoB;gBACpB,aAAa;gBACb,OAAO,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC9C,CAAC;YAAC,MAAM,CAAC;gBACP,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC,CAAC;YAC7E,CAAC;QACH,CAAC;IACH,CAAC;IACD,sGAAsG;IACtG,SAAS,CAAC,IAAY;QACpB,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QACrD,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;QAC7B,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QACrC,SAAS,CAAC,MAAM,EAAE,CAAC;QACnB,IAAI,CAAC;YACH,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;gBAAS,CAAC;YACT,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IACD,KAAK,CAAC,cAAc,CAAC,IAAY,EAAE,OAAyB;QAC1D,QAAQ,OAAO,CAAC,WAAW,EAAE,CAAC;YAC5B,KAAK,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;gBACvB,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;oBACzB,MAAM,IAAI,6BAA6B,EAAE,CAAC;gBAC5C,CAAC;gBAED,IAAI,CAAC;oBACH,MAAM,kBAAkB,GAAG,uBAAuB,CAAC,IAAI,CAAC,CAAC;oBACzD,MAAM,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC;oBACtD,OAAO,IAAI,CAAC;gBACd,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,gDAAgD;oBAChD,IACE,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,EAAE,IAAI,KAAK,iBAAiB,CAAC;wBAChE,CAAC,MAAM,gCAAgC,EAAE,CAAC,EAC1C,CAAC;wBACD,MAAM,IAAI,qBAAqB,EAAE,CAAC;oBACpC,CAAC;oBACD,MAAM,IAAI,oBAAoB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBAChD,CAAC;YACH,CAAC;YACD,OAAO,CAAC,CAAC,CAAC;gBACR,IAAI,CAAC;oBACH,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;wBACzB,MAAM,IAAI,KAAK,EAAE,CAAC;oBACpB,CAAC;oBACD,MAAM,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;oBAC1C,OAAO,IAAI,CAAC;gBACd,CAAC;gBAAC,MAAM,CAAC;oBACP,6DAA6D;oBAC7D,+CAA+C;oBAC/C,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC9B,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IACD,KAAK,CAAC,cAAc;QAClB,OAAO,MAAM,sBAAsB,CAAC,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC,CAAC;IACnE,CAAC;IACD,KAAK,CAAC,aAAa,CAAC,QAAyB;QAC3C,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;YACzB,MAAM,IAAI,6BAA6B,EAAE,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,MAAM,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YACxD,MAAM,IAAI,GAAG,MAAM,yBAAyB,CAAC,cAAc,CAAC,CAAC;YAC7D,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACrC,iBAAiB,CAAC,IAAI,CAAC;gBACvB,yBAAyB,CAAC,IAAI,CAAC;aAChC,CAAC,CAAC;YAEH,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACxB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,gDAAgD;YAChD,IACE,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,EAAE,IAAI,KAAK,iBAAiB,CAAC;gBAChE,CAAC,MAAM,gCAAgC,EAAE,CAAC,EAC1C,CAAC;gBACD,MAAM,IAAI,qBAAqB,EAAE,CAAC;YACpC,CAAC;YACD,MAAM,IAAI,qBAAqB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IACD,KAAK,CAAC,aAAa,CAAC,WAAmB;QACrC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;YACzB,MAAM,IAAI,6BAA6B,EAAE,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC;YACH,0FAA0F;YAC1F,oDAAoD;YACpD,MAAM,IAAI,GAAG,YAAY,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YACpD,MAAM,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC;gBAC9B,IAAI,aAAa,CAAC;oBAChB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI;iBAClB,CAAC;aACH,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,MAAM,IAAI,oBAAoB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IACD,KAAK,CAAC,aAAa;QACjB,OAAO,MAAM,sBAAsB,CAAC,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC;IACnE,CAAC;IACD,oBAAoB,KAAU,CAAC;IAC/B,uBAAuB,KAAU,CAAC;CACnC,CAAC;AAEF;;;;GAIG;AACH,KAAK,UAAU,sBAAsB,CAAC,KAAe;IACnD,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QACzB,MAAM,IAAI,6BAA6B,EAAE,CAAC;IAC5C,CAAC;IAED,IAAI,CAAC;QACH,MAAM,cAAc,GAAG,MAAM,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACxD,OAAO,cAAc,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3F,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,gDAAgD;QAChD,IACE,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,EAAE,IAAI,KAAK,iBAAiB,CAAC;YAChE,CAAC,MAAM,gCAAgC,EAAE,CAAC,EAC1C,CAAC;YACD,MAAM,IAAI,qBAAqB,EAAE,CAAC;QACpC,CAAC;QACD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED,SAAS,uBAAuB,CAAC,UAAkB;IACjD,OAAO,IAAI,aAAa,CAAC;QACvB,sFAAsF;QACtF,WAAW,EAAE,IAAI,IAAI,CAAC,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;QAC1D,sFAAsF;QACtF,YAAY,EAAE,IAAI,IAAI,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;KAC9E,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import {\n  ClipboardUnavailableException,\n  CopyFailureException,\n  NoPermissionException,\n  PasteFailureException,\n} from './Exceptions';\nimport {\n  base64toBlob,\n  blobToBase64Async,\n  findHtmlInClipboardAsync,\n  findImageInClipboardAsync,\n  getImageSizeFromBlobAsync,\n  htmlToPlainText,\n  isClipboardPermissionDeniedAsync,\n} from './Utils';\nimport {\n  ClipboardImage,\n  GetImageOptions,\n  GetStringOptions,\n  SetStringOptions,\n  StringFormat,\n} from '../Clipboard.types';\n\nexport default {\n  async getStringAsync(options: GetStringOptions): Promise<string> {\n    if (!navigator.clipboard) {\n      throw new ClipboardUnavailableException();\n    }\n\n    try {\n      switch (options.preferredFormat) {\n        case StringFormat.HTML: {\n          // Try reading HTML first\n          const clipboardItems = await navigator.clipboard.read();\n          const blob = await findHtmlInClipboardAsync(clipboardItems);\n          if (!blob) {\n            // Fall back to plain text\n            return await navigator.clipboard.readText();\n          }\n          return await new Response(blob).text();\n        }\n        default: {\n          let text = await navigator.clipboard.readText();\n          if (!text || text === '') {\n            // If there's no direct plain text, try reading HTML\n            const clipboardItems = await navigator.clipboard.read();\n            const blob = await findHtmlInClipboardAsync(clipboardItems);\n            const blobText = await blob?.text();\n            text = htmlToPlainText(blobText ?? '');\n          }\n          return text;\n        }\n      }\n    } catch (error: any) {\n      // it might fail, because user denied permission\n      if (\n        (typeof error === 'object' && error?.name === 'NotAllowedError') ||\n        (await isClipboardPermissionDeniedAsync())\n      ) {\n        throw new NoPermissionException();\n      }\n\n      try {\n        // Internet Explorer\n        // @ts-ignore\n        return window.clipboardData.getData('Text');\n      } catch {\n        return Promise.reject(new Error('Unable to retrieve item from clipboard'));\n      }\n    }\n  },\n  // TODO: (barthap) The `setString` was deprecated in SDK 45. Remove this function in a few SDK cycles.\n  setString(text: string): boolean {\n    const textField = document.createElement('textarea');\n    textField.textContent = text;\n    document.body.appendChild(textField);\n    textField.select();\n    try {\n      document.execCommand('copy');\n      return true;\n    } catch {\n      return false;\n    } finally {\n      document.body.removeChild(textField);\n    }\n  },\n  async setStringAsync(text: string, options: SetStringOptions): Promise<boolean> {\n    switch (options.inputFormat) {\n      case StringFormat.HTML: {\n        if (!navigator.clipboard) {\n          throw new ClipboardUnavailableException();\n        }\n\n        try {\n          const clipboardItemInput = createHtmlClipboardItem(text);\n          await navigator.clipboard.write([clipboardItemInput]);\n          return true;\n        } catch (error: any) {\n          // it might fail, because user denied permission\n          if (\n            (typeof error === 'object' && error?.name === 'NotAllowedError') ||\n            (await isClipboardPermissionDeniedAsync())\n          ) {\n            throw new NoPermissionException();\n          }\n          throw new CopyFailureException(error.message);\n        }\n      }\n      default: {\n        try {\n          if (!navigator.clipboard) {\n            throw new Error();\n          }\n          await navigator.clipboard.writeText(text);\n          return true;\n        } catch {\n          // we can fall back to legacy behavior in any kind of failure\n          // including navigator.clipboard unavailability\n          return this.setString(text);\n        }\n      }\n    }\n  },\n  async hasStringAsync(): Promise<boolean> {\n    return await clipboardHasTypesAsync(['text/plain', 'text/html']);\n  },\n  async getImageAsync(_options: GetImageOptions): Promise<ClipboardImage | null> {\n    if (!navigator.clipboard) {\n      throw new ClipboardUnavailableException();\n    }\n\n    try {\n      const clipboardItems = await navigator.clipboard.read();\n      const blob = await findImageInClipboardAsync(clipboardItems);\n      if (!blob) {\n        return null;\n      }\n\n      const [data, size] = await Promise.all([\n        blobToBase64Async(blob),\n        getImageSizeFromBlobAsync(blob),\n      ]);\n\n      return { data, size };\n    } catch (error: any) {\n      // it might fail, because user denied permission\n      if (\n        (typeof error === 'object' && error?.name === 'NotAllowedError') ||\n        (await isClipboardPermissionDeniedAsync())\n      ) {\n        throw new NoPermissionException();\n      }\n      throw new PasteFailureException(error.message);\n    }\n  },\n  async setImageAsync(base64image: string): Promise<void> {\n    if (!navigator.clipboard) {\n      throw new ClipboardUnavailableException();\n    }\n\n    try {\n      // we set it always to `image/png` because it's the only format supported by the clipboard\n      // but it seems to work even when provided jpeg data\n      const blob = base64toBlob(base64image, 'image/png');\n      await navigator.clipboard.write([\n        new ClipboardItem({\n          [blob.type]: blob,\n        }),\n      ]);\n    } catch (err: any) {\n      throw new CopyFailureException(err.message);\n    }\n  },\n  async hasImageAsync(): Promise<boolean> {\n    return await clipboardHasTypesAsync(['image/png', 'image/jpeg']);\n  },\n  addClipboardListener(): void {},\n  removeClipboardListener(): void {},\n};\n\n/**\n * Resolves to true if clipboard has one of provided {@link types}.\n * @throws `ClipboardUnavailableException` if AsyncClipboard API is not available\n * @throws `NoPermissionException` if user denied permission\n */\nasync function clipboardHasTypesAsync(types: string[]): Promise<boolean> {\n  if (!navigator.clipboard) {\n    throw new ClipboardUnavailableException();\n  }\n\n  try {\n    const clipboardItems = await navigator.clipboard.read();\n    return clipboardItems.flatMap((item) => item.types).some((type) => types.includes(type));\n  } catch (error: any) {\n    // it might fail, because user denied permission\n    if (\n      (typeof error === 'object' && error?.name === 'NotAllowedError') ||\n      (await isClipboardPermissionDeniedAsync())\n    ) {\n      throw new NoPermissionException();\n    }\n    throw error;\n  }\n}\n\nfunction createHtmlClipboardItem(htmlString: string): ClipboardItem {\n  return new ClipboardItem({\n    // @ts-ignore `Blob` from `lib.dom.d.ts` and the one from `@types/react-native` differ\n    'text/html': new Blob([htmlString], { type: 'text/html' }),\n    // @ts-ignore `Blob` from `lib.dom.d.ts` and the one from `@types/react-native` differ\n    'text/plain': new Blob([htmlToPlainText(htmlString)], { type: 'text/plain' }),\n  });\n}\n"]}