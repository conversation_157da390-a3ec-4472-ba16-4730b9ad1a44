{"version": 3, "file": "Utils.js", "sourceRoot": "", "sources": ["../../src/web/Utils.ts"], "names": [], "mappings": "AAAA;;;GAGG;AACH,MAAM,UAAU,YAAY,CAAC,UAAkB,EAAE,WAAmB;IAClE,WAAW,GAAG,WAAW,IAAI,EAAE,CAAC;IAChC,MAAM,SAAS,GAAG,IAAI,CAAC;IACvB,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;IACxC,MAAM,WAAW,GAAG,cAAc,CAAC,MAAM,CAAC;IAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,CAAC;IACvD,MAAM,UAAU,GAAG,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;IAE1C,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,WAAW,EAAE,EAAE,UAAU,EAAE,CAAC;QAChE,MAAM,KAAK,GAAG,UAAU,GAAG,SAAS,CAAC;QACrC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,SAAS,EAAE,WAAW,CAAC,CAAC;QAErD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;QACrC,KAAK,IAAI,MAAM,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC;YAC5D,KAAK,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC;QACD,UAAU,CAAC,UAAU,CAAC,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;IACjD,CAAC;IACD,wFAAwF;IACxF,0BAA0B;IAC1B,2BAA2B;IAC3B,8FAA8F;IAC9F,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;AACrD,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAC,IAAU;IAC1C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE;QAChC,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;QAChC,MAAM,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,MAAgB,CAAC,CAAC;QAC1D,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;AACL,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,IAAY;IAC1C,MAAM,cAAc,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IACrD,cAAc,CAAC,SAAS,GAAG,IAAI,CAAC;IAChC,OAAO,cAAc,CAAC,WAAW,IAAI,cAAc,CAAC,SAAS,IAAI,EAAE,CAAC;AACtE,CAAC;AAED,MAAM,UAAU,yBAAyB,CAAC,IAAU;IAClD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE;QAChC,MAAM,OAAO,GAAG,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC1C,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC1C,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC;QAClB,GAAG,CAAC,MAAM,GAAG;YACX,OAAO,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;QACpD,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,yBAAyB,CAAC,KAAqB;IACnE,KAAK,MAAM,aAAa,IAAI,KAAK,EAAE,CAAC;QAClC,qBAAqB;QACrB,IAAI,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,WAAW,CAAC,EAAE,CAAC;YAC7D,OAAO,MAAM,aAAa,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAClD,CAAC;QAED,0CAA0C;QAC1C,oFAAoF;QACpF,IAAI,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,YAAY,CAAC,EAAE,CAAC;YAC9D,OAAO,MAAM,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,wBAAwB,CAAC,KAAqB;IAClE,KAAK,MAAM,aAAa,IAAI,KAAK,EAAE,CAAC;QAClC,IAAI,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,WAAW,CAAC,EAAE,CAAC;YAC7D,OAAO,MAAM,aAAa,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,gCAAgC;IACpD,MAAM,SAAS,GAAG,EAAE,IAAI,EAAE,gBAAkC,EAAE,CAAC;IAC/D,MAAM,gBAAgB,GAAG,MAAM,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IACtE,OAAO,gBAAgB,CAAC,KAAK,KAAK,QAAQ,CAAC;AAC7C,CAAC", "sourcesContent": ["/**\n * Converts base64-encoded data to a `Blob` object.\n * @see https://stackoverflow.com/a/20151856\n */\nexport function base64toBlob(base64Data: string, contentType: string): Blob {\n  contentType = contentType || '';\n  const sliceSize = 1024;\n  const byteCharacters = atob(base64Data);\n  const bytesLength = byteCharacters.length;\n  const slicesCount = Math.ceil(bytesLength / sliceSize);\n  const byteArrays = new Array(slicesCount);\n\n  for (let sliceIndex = 0; sliceIndex < slicesCount; ++sliceIndex) {\n    const begin = sliceIndex * sliceSize;\n    const end = Math.min(begin + sliceSize, bytesLength);\n\n    const bytes = new Array(end - begin);\n    for (let offset = begin, i = 0; offset < end; ++i, ++offset) {\n      bytes[i] = byteCharacters[offset].charCodeAt(0);\n    }\n    byteArrays[sliceIndex] = new Uint8Array(bytes);\n  }\n  // I cannot use `@ts-expect-error` here because some environments consider this correct:\n  // expo-module build - OK,\n  // expo-module test - error\n  // @ts-ignore `Blob` from `lib.dom.d.ts` and the one from `@types/react-native` differ somehow\n  return new Blob(byteArrays, { type: contentType });\n}\n\n/**\n * Converts blob to base64-encoded string with Data-URL prefix.\n */\nexport function blobToBase64Async(blob: Blob): Promise<string> {\n  return new Promise((resolve, _) => {\n    const reader = new FileReader();\n    reader.onloadend = () => resolve(reader.result as string);\n    reader.readAsDataURL(blob);\n  });\n}\n\nexport function htmlToPlainText(html: string) {\n  const tempDivElement = document.createElement('div');\n  tempDivElement.innerHTML = html;\n  return tempDivElement.textContent || tempDivElement.innerText || '';\n}\n\nexport function getImageSizeFromBlobAsync(blob: Blob): Promise<{ width: number; height: number }> {\n  return new Promise((resolve, _) => {\n    const blobUrl = URL.createObjectURL(blob);\n    const img = document.createElement('img');\n    img.src = blobUrl;\n    img.onload = function () {\n      resolve({ width: img.width, height: img.height });\n    };\n  });\n}\n\nexport async function findImageInClipboardAsync(items: ClipboardItems): Promise<Blob | null> {\n  for (const clipboardItem of items) {\n    // first look for png\n    if (clipboardItem.types.some((type) => type === 'image/png')) {\n      return await clipboardItem.getType('image/png');\n    }\n\n    // alternatively, an image might be a jpeg\n    // NOTE: Currently, this is not supported by browsers yet. They only support PNG now\n    if (clipboardItem.types.some((type) => type === 'image/jpeg')) {\n      return await clipboardItem.getType('image/jpeg');\n    }\n  }\n  return null;\n}\n\nexport async function findHtmlInClipboardAsync(items: ClipboardItems): Promise<Blob | null> {\n  for (const clipboardItem of items) {\n    if (clipboardItem.types.some((type) => type === 'text/html')) {\n      return await clipboardItem.getType('text/html');\n    }\n  }\n  return null;\n}\n\nexport async function isClipboardPermissionDeniedAsync(): Promise<boolean> {\n  const queryOpts = { name: 'clipboard-read' as PermissionName };\n  const permissionStatus = await navigator.permissions.query(queryOpts);\n  return permissionStatus.state === 'denied';\n}\n"]}