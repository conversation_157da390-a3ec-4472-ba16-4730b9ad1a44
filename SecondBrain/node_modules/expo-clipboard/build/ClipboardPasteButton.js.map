{"version": 3, "file": "ClipboardPasteButton.js", "sourceRoot": "", "sources": ["../src/ClipboardPasteButton.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAC;AAU1B,OAAO,wBAAwB,MAAM,4BAA4B,CAAC;AA+DlE,cAAc;AACd;;;;;;;;;;;;;;;;GAgBG;AACH,MAAM,UAAU,oBAAoB,CAAC,EAAE,OAAO,EAAE,GAAG,SAAS,EAA6B;IACvF,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAC9B,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACvD,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,cAAc,GAAG,CAAC,EAAE,WAAW,EAA2C,EAAE,EAAE;QAClF,OAAO,CAAC,WAAW,CAAC,CAAC;IACvB,CAAC,CAAC;IAEF,OAAO,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC,cAAc,CAAC,CAAC,IAAI,SAAS,CAAC,EAAG,CAAC;AACrF,CAAC", "sourcesContent": ["import React from 'react';\nimport { NativeSyntheticEvent, StyleProp, ViewProps, ViewStyle } from 'react-native';\n\nimport {\n  AcceptedContentType,\n  CornerStyleType,\n  DisplayModeType,\n  GetImageOptions,\n  PasteEventPayload,\n} from './Clipboard.types';\nimport ExpoClipboardPasteButton from './ExpoClipboardPasteButton';\n\n// @needsAudit\nexport type ClipboardPasteButtonProps = {\n  /**\n   * A callback that is called with the result of the paste action.\n   * Inspect the `type` property to determine the type of the pasted data.\n   *\n   * Can be one of `text` or `image`.\n   *\n   * @example\n   * ```ts\n   *   onPress={(data) => {\n   *     if (data.type === 'image') {\n   *       setImageData(data);\n   *    } else {\n   *       setTextData(data);\n   *     }\n   *   }}\n   * ```\n   */\n  onPress: (data: PasteEventPayload) => void;\n  /**\n   * The backgroundColor of the button.\n   * Leaving this as the default allows the color to adjust to the system theme settings.\n   */\n  backgroundColor?: string | null;\n  /**\n   * The foregroundColor of the button.\n   * @default 'white'\n   */\n  foregroundColor?: string | null;\n  /**\n   * The cornerStyle of the button.\n   * @default 'capsule'\n   *\n   * @see [Apple Documentation](https://developer.apple.com/documentation/uikit/uibutton/configuration/cornerstyle) for more details.\n   */\n  cornerStyle?: CornerStyleType | null;\n  /**\n   * The displayMode of the button.\n   * @default 'iconAndLabel'\n   *\n   * @see [Apple Documentation](https://developer.apple.com/documentation/uikit/uipastecontrol/displaymode) for more details.\n   */\n  displayMode?: DisplayModeType | null;\n  /**\n   * The custom style to apply to the button. Should not include `backgroundColor`, `borderRadius` or `color`\n   * properties.\n   */\n  style?: StyleProp<Omit<ViewStyle, 'backgroundColor' | 'borderRadius' | 'color'>>;\n  /**\n   * The options to use when pasting an image from the clipboard.\n   */\n  imageOptions?: GetImageOptions | null;\n  /**\n   * An array of the content types that will cause the button to become active.\n   * > Do not include `plain-text` and `html` at the same time as this will cause all text to be treated as `html`.\n   * @default ['plain-text', 'image']\n   */\n  acceptedContentTypes?: AcceptedContentType[];\n} & ViewProps;\n\n// @needsAudit\n/**\n * This component displays the `UIPasteControl` button on your screen. This allows pasting from the clipboard without requesting permission from the user.\n *\n * You should only attempt to render this if [`Clipboard.isPasteButtonAvailable`](#ispastebuttonavailable)\n * is `true`. This component will render nothing if it is not available, and you will get\n * a warning in development mode (`__DEV__ === true`).\n *\n * The properties of this component extend from `View`; however, you should not attempt to set\n * `backgroundColor`, `color` or `borderRadius` with the `style` property. Apple restricts customisation of this view.\n * Instead, you should use the backgroundColor and foregroundColor properties to set the colors of the button, the cornerStyle property to change the border radius,\n * and the displayMode property to change the appearance of the icon and label. The word \"Paste\" is not editable and neither is the icon.\n *\n * Make sure to attach height and width via the style props as without these styles, the button will\n * not appear on the screen.\n *\n * @see [Apple Documentation](https://developer.apple.com/documentation/uikit/uipastecontrol) for more details.\n */\nexport function ClipboardPasteButton({ onPress, ...restProps }: ClipboardPasteButtonProps) {\n  if (!ExpoClipboardPasteButton) {\n    if (__DEV__) {\n      console.warn(\"'ApplePasteButton' is not available.\");\n    }\n    return null;\n  }\n\n  const onPastePressed = ({ nativeEvent }: NativeSyntheticEvent<PasteEventPayload>) => {\n    onPress(nativeEvent);\n  };\n\n  return <ExpoClipboardPasteButton onPastePressed={onPastePressed} {...restProps} />;\n}\n"]}