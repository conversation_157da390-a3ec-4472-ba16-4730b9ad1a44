{"version": 3, "file": "ExpoClipboardPasteButton.js", "sourceRoot": "", "sources": ["../src/ExpoClipboardPasteButton.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,wBAAwB,EAAE,MAAM,mBAAmB,CAAC;AAC7D,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAExC,IAAI,aAAkB,CAAC;AAEvB,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;IAC1B,aAAa,GAAG,wBAAwB,CAAC,eAAe,CAAC,CAAC;AAC5D,CAAC;AAED,eAAe,aAAa,CAAC", "sourcesContent": ["import { requireNativeViewManager } from 'expo-modules-core';\nimport { Platform } from 'react-native';\n\nlet ExpoClipboard: any;\n\nif (Platform.OS === 'ios') {\n  ExpoClipboard = requireNativeViewManager('ExpoClipboard');\n}\n\nexport default ExpoClipboard;\n"]}