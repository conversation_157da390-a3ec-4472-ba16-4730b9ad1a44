{"version": 3, "file": "Clipboard.js", "sourceRoot": "", "sources": ["../src/Clipboard.ts"], "names": [], "mappings": "AAAA,OAAO,EAA0B,mBAAmB,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAS1F,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,aAAa,MAAM,iBAAiB,CAAC;AAE5C,MAAM,oBAAoB,GAAG,oBAAoB,CAAC;AAelD;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,cAAc,CAAC,UAA4B,EAAE;IACjE,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;QAClC,MAAM,IAAI,mBAAmB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;IAC/D,CAAC;IACD,OAAO,MAAM,aAAa,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AACrD,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,CAAC,KAAK,UAAU,cAAc,CAClC,IAAY,EACZ,UAA4B,EAAE;IAE9B,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;QAClC,MAAM,IAAI,mBAAmB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;IAC/D,CAAC;IACD,OAAO,aAAa,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACrD,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,SAAS,CAAC,IAAY;IACpC,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;QAC1B,2CAA2C;QAC3C,mCAAmC;QACnC,OAAO,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;SAAM,CAAC;QACN,cAAc,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;AACH,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,cAAc;IAC5B,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;QAClC,MAAM,IAAI,mBAAmB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;IAC/D,CAAC;IACD,OAAO,aAAa,CAAC,cAAc,EAAE,CAAC;AACxC,CAAC;AAED;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,WAAW;IAC/B,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;QAC/B,MAAM,IAAI,mBAAmB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;IAC5D,CAAC;IACD,OAAO,MAAM,aAAa,CAAC,WAAW,EAAE,CAAC;AAC3C,CAAC;AAED;;;;;;;;;GASG;AACH,MAAM,CAAC,KAAK,UAAU,WAAW,CAAC,GAAW;IAC3C,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;QAC/B,MAAM,IAAI,mBAAmB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;IAC5D,CAAC;IACD,OAAO,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AACxC,CAAC;AAED;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,WAAW;IAC/B,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;QAC/B,MAAM,IAAI,mBAAmB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;IAC5D,CAAC;IACD,OAAO,MAAM,aAAa,CAAC,WAAW,EAAE,CAAC;AAC3C,CAAC;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,MAAM,CAAC,KAAK,UAAU,aAAa,CAAC,OAAwB;IAC1D,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;QACjC,MAAM,IAAI,mBAAmB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;IAC9D,CAAC;IACD,OAAO,MAAM,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AACpD,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,MAAM,CAAC,KAAK,UAAU,aAAa,CAAC,WAAmB;IACrD,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;QACjC,MAAM,IAAI,mBAAmB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;IAC9D,CAAC;IACD,OAAO,aAAa,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;AAClD,CAAC;AAED;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,aAAa;IACjC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;QACjC,MAAM,IAAI,mBAAmB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;IAC9D,CAAC;IACD,OAAO,aAAa,CAAC,aAAa,EAAE,CAAC;AACvC,CAAC;AAED;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,UAAU,oBAAoB,CAAC,QAAyC;IAC5E,gGAAgG;IAChG,MAAM,eAAe,GAAG,CAAC,KAAqB,EAAE,EAAE;QAChD,MAAM,YAAY,GAAmB;YACnC,GAAG,KAAK;YACR,IAAI,OAAO;gBACT,OAAO,CAAC,IAAI,CACV,sHAAsH,CACvH,CAAC;gBACF,OAAO,EAAE,CAAC;YACZ,CAAC;SACF,CAAC;QACF,QAAQ,CAAC,YAAY,CAAC,CAAC;IACzB,CAAC,CAAC;IACF,OAAO,aAAa,CAAC,WAAW,CAAC,oBAAoB,EAAE,eAAe,CAAC,CAAC;AAC1E,CAAC;AAED;;;;;;;;;;;;GAYG;AACH,MAAM,UAAU,uBAAuB,CAAC,YAA+B;IACrE,YAAY,CAAC,MAAM,EAAE,CAAC;AACxB,CAAC;AAED;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,sBAAsB,GACjC,QAAQ,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC,CAAC,KAAK,CAAC;AAEvE,cAAc,mBAAmB,CAAC;AAGlC,OAAO,EAAE,oBAAoB,EAAE,CAAC", "sourcesContent": ["import { type EventSubscription, UnavailabilityError, Platform } from 'expo-modules-core';\n\nimport type {\n  ClipboardImage,\n  ContentType,\n  GetImageOptions,\n  GetStringOptions,\n  SetStringOptions,\n} from './Clipboard.types';\nimport { ClipboardPasteButton } from './ClipboardPasteButton';\nimport ExpoClipboard from './ExpoClipboard';\n\nconst onClipboardEventName = 'onClipboardChanged';\n\ntype ClipboardEvent = {\n  /**\n   * @deprecated Returns empty string. Use [`getStringAsync()`](#getstringasyncoptions) instead to retrieve clipboard content.\n   */\n  content: string;\n  /**\n   * An array of content types that are available on the clipboard.\n   */\n  contentTypes: ContentType[];\n};\n\nexport { EventSubscription as Subscription, ClipboardEvent };\n\n/**\n * Gets the content of the user's clipboard. Calling this method on web will prompt\n * the user to grant your app permission to \"see text and images copied to the clipboard.\"\n *\n * @param options Options for the clipboard content to be retrieved.\n * @returns A promise that resolves to the content of the clipboard.\n */\nexport async function getStringAsync(options: GetStringOptions = {}): Promise<string> {\n  if (!ExpoClipboard.getStringAsync) {\n    throw new UnavailabilityError('Clipboard', 'getStringAsync');\n  }\n  return await ExpoClipboard.getStringAsync(options);\n}\n\n/**\n * Sets the content of the user's clipboard.\n *\n * @param text The string to save to the clipboard.\n * @param options Options for the clipboard content to be set.\n * @returns On web, this returns a promise that fulfills to a boolean value indicating whether or not\n * the string was saved to the user's clipboard. On iOS and Android, the promise always resolves to `true`.\n */\nexport async function setStringAsync(\n  text: string,\n  options: SetStringOptions = {}\n): Promise<boolean> {\n  if (!ExpoClipboard.setStringAsync) {\n    throw new UnavailabilityError('Clipboard', 'setStringAsync');\n  }\n  return ExpoClipboard.setStringAsync(text, options);\n}\n\n/**\n * Sets the content of the user's clipboard.\n * @deprecated Use [`setStringAsync()`](#setstringasynctext-options) instead.\n *\n * @returns On web, this returns a boolean value indicating whether or not the string was saved to\n * the user's clipboard. On iOS and Android, nothing is returned.\n */\nexport function setString(text: string): void {\n  if (Platform.OS === 'web') {\n    // on web, we need to return legacy method,\n    // because of different return type\n    return ExpoClipboard.setString(text);\n  } else {\n    setStringAsync(text);\n  }\n}\n\n/**\n * Returns whether the clipboard has text content. Returns true for both plain text and rich text (e.g. HTML).\n *\n * On web, this requires the user to grant your app permission to _\"see text and images copied to the clipboard\"_.\n *\n * @returns A promise that fulfills to `true` if clipboard has text content, resolves to `false` otherwise.\n */\nexport function hasStringAsync(): Promise<boolean> {\n  if (!ExpoClipboard.hasStringAsync) {\n    throw new UnavailabilityError('Clipboard', 'hasStringAsync');\n  }\n  return ExpoClipboard.hasStringAsync();\n}\n\n/**\n * Gets the URL from the user's clipboard.\n *\n * @returns A promise that fulfills to the URL in the clipboard.\n * @platform ios\n */\nexport async function getUrlAsync(): Promise<string | null> {\n  if (!ExpoClipboard.getUrlAsync) {\n    throw new UnavailabilityError('Clipboard', 'getUrlAsync');\n  }\n  return await ExpoClipboard.getUrlAsync();\n}\n\n/**\n * Sets a URL in the user's clipboard.\n *\n * This function behaves the same as [`setStringAsync()`](#setstringasynctext-options), except that\n * it sets the clipboard content type to be a URL. It lets your app or other apps know that the\n * clipboard contains a URL and behave accordingly.\n *\n * @param url The URL to save to the clipboard.\n * @platform ios\n */\nexport async function setUrlAsync(url: string): Promise<void> {\n  if (!ExpoClipboard.setUrlAsync) {\n    throw new UnavailabilityError('Clipboard', 'setUrlAsync');\n  }\n  return ExpoClipboard.setUrlAsync(url);\n}\n\n/**\n * Returns whether the clipboard has a URL content.\n *\n * @returns A promise that fulfills to `true` if clipboard has URL content, resolves to `false` otherwise.\n * @platform ios\n */\nexport async function hasUrlAsync(): Promise<boolean> {\n  if (!ExpoClipboard.hasUrlAsync) {\n    throw new UnavailabilityError('Clipboard', 'hasUrlAsync');\n  }\n  return await ExpoClipboard.hasUrlAsync();\n}\n\n/**\n * Gets the image from the user's clipboard and returns it in the specified\n * format. Calling this method on web will prompt the user to grant your app\n * permission to \"see text and images copied to the clipboard.\"\n *\n * @param options A `GetImageOptions` object to specify the desired format of the image.\n * @returns If there was an image in the clipboard, the promise resolves to\n * a [`ClipboardImage`](#clipboardimage) object containing the base64 string and metadata of the image.\n * Otherwise, it resolves to `null`.\n *\n * @example\n * ```tsx\n * const img = await Clipboard.getImageAsync({ format: 'png' });\n * // ...\n * <Image source={{ uri: img?.data }} style={{ width: 200, height: 200 }} />\n * ```\n */\nexport async function getImageAsync(options: GetImageOptions): Promise<ClipboardImage | null> {\n  if (!ExpoClipboard.getImageAsync) {\n    throw new UnavailabilityError('Clipboard', 'getImageAsync');\n  }\n  return await ExpoClipboard.getImageAsync(options);\n}\n\n/**\n * Sets an image in the user's clipboard.\n *\n * @param base64Image Image encoded as a base64 string, without MIME type.\n *\n * @example\n * ```tsx\n * const result = await ImagePicker.launchImageLibraryAsync({\n *   mediaTypes: ImagePicker.MediaTypeOptions.Images,\n *   base64: true,\n * });\n * await Clipboard.setImageAsync(result.base64);\n * ```\n */\nexport async function setImageAsync(base64Image: string): Promise<void> {\n  if (!ExpoClipboard.setImageAsync) {\n    throw new UnavailabilityError('Clipboard', 'setImageAsync');\n  }\n  return ExpoClipboard.setImageAsync(base64Image);\n}\n\n/**\n * Returns whether the clipboard has an image content.\n *\n * On web, this requires the user to grant your app permission to _\"see text and images copied to the clipboard\"_.\n *\n * @returns A promise that fulfills to `true` if clipboard has image content, resolves to `false` otherwise.\n */\nexport async function hasImageAsync(): Promise<boolean> {\n  if (!ExpoClipboard.hasImageAsync) {\n    throw new UnavailabilityError('Clipboard', 'hasImageAsync');\n  }\n  return ExpoClipboard.hasImageAsync();\n}\n\n/**\n * Adds a listener that will fire whenever the content of the user's clipboard changes. This method\n * is a no-op on Web.\n *\n * @param listener Callback to execute when listener is triggered. The callback is provided a\n * single argument that is an object containing information about clipboard contents.\n *\n * @example\n * ```typescript\n * Clipboard.addClipboardListener(({ contentTypes }: ClipboardEvent) => {\n *   if (contentTypes.includes(Clipboard.ContentType.PLAIN_TEXT)) {\n *     Clipboard.getStringAsync().then(content => {\n *       alert('Copy pasta! Here\\'s the string that was copied: ' + content)\n *     });\n *   } else if (contentTypes.includes(Clipboard.ContentType.IMAGE)) {\n *     alert('Yay! Clipboard contains an image');\n *   }\n * });\n * ```\n */\nexport function addClipboardListener(listener: (event: ClipboardEvent) => void): EventSubscription {\n  // TODO: Get rid of this wrapper once we remove deprecated `content` property (not before SDK47)\n  const listenerWrapper = (event: ClipboardEvent) => {\n    const wrappedEvent: ClipboardEvent = {\n      ...event,\n      get content(): string {\n        console.warn(\n          \"The 'content' property of the clipboard event is deprecated. Use 'getStringAsync()' instead to get clipboard content\"\n        );\n        return '';\n      },\n    };\n    listener(wrappedEvent);\n  };\n  return ExpoClipboard.addListener(onClipboardEventName, listenerWrapper);\n}\n\n/**\n * Removes the listener added by addClipboardListener. This method is a no-op on Web.\n *\n * @param subscription The subscription to remove (created by addClipboardListener).\n *\n * @example\n * ```typescript\n * const subscription = addClipboardListener(() => {\n *   alert('Copy pasta!');\n * });\n * removeClipboardListener(subscription);\n * ```\n */\nexport function removeClipboardListener(subscription: EventSubscription) {\n  subscription.remove();\n}\n\n/**\n * Property that determines if the `ClipboardPasteButton` is available.\n *\n * This requires the users device to be using at least iOS 16.\n *\n * `true` if the component is available, and `false` otherwise.\n */\nexport const isPasteButtonAvailable: boolean =\n  Platform.OS === 'ios' ? ExpoClipboard.isPasteButtonAvailable : false;\n\nexport * from './Clipboard.types';\nexport { ClipboardPasteButtonProps } from './ClipboardPasteButton';\n\nexport { ClipboardPasteButton };\n"]}