{"version": 3, "file": "Clipboard.types.js", "sourceRoot": "", "sources": ["../src/Clipboard.types.ts"], "names": [], "mappings": "AAuCA;;GAEG;<PERSON>CH,MAAM,CAAN,IAAY,WAQX;AARD,WAAY,WAAW;IACrB,wCAAyB,CAAA;IACzB,4BAAa,CAAA;IACb,8BAAe,CAAA;IACf;;OAEG;IACH,0BAAW,CAAA;AACb,CAAC,EARW,WAAW,KAAX,WAAW,QAQtB;AAED;;GAEG;AACH,MAAM,CAAN,IAAY,YAGX;AAHD,WAAY,YAAY;IACtB,wCAAwB,CAAA;IACxB,6BAAa,CAAA;AACf,CAAC,EAHW,YAAY,KAAZ,YAAY,QAGvB", "sourcesContent": ["// @needsAudit\nexport type GetImageOptions = {\n  /**\n   * The format of the clipboard image to be converted to.\n   */\n  format: 'png' | 'jpeg';\n  /**\n   * Specify the quality of the returned image, between `0` and `1`. Defaults to `1` (highest quality).\n   * Applicable only when `format` is set to `jpeg`, ignored otherwise.\n   * @default 1\n   */\n  jpegQuality?: number;\n};\n\n// @needsAudit\nexport type ClipboardImage = {\n  /**\n   * A Base64-encoded string of the image data. Its format is dependent on the `format` option.\n   * You can use it directly as the source of an `Image` element.\n   *\n   * > **NOTE:** The string is already prepended with `data:image/png;base64,` or `data:image/jpeg;base64,` prefix.\n   * @example\n   * ```ts\n   * <Image\n   *   source={{ uri: clipboardImage.data }}\n   *   style={{ width: 200, height: 200 }}\n   * />\n   * ```\n   */\n  data: string;\n  /**\n   * Dimensions (`width` and `height`) of the image pasted from clipboard.\n   */\n  size: {\n    width: number;\n    height: number;\n  };\n};\n\n/**\n * Type used to define what type of data is stored in the clipboard.\n */\nexport enum ContentType {\n  PLAIN_TEXT = 'plain-text',\n  HTML = 'html',\n  IMAGE = 'image',\n  /**\n   * @platform iOS\n   */\n  URL = 'url',\n}\n\n/**\n * Type used to determine string format stored in the clipboard.\n */\nexport enum StringFormat {\n  PLAIN_TEXT = 'plainText',\n  HTML = 'html',\n}\n\nexport type GetStringOptions = {\n  /**\n   * The target format of the clipboard string to be converted to, if possible.\n   *\n   * @default StringFormat.PLAIN_TEXT\n   */\n  preferredFormat?: StringFormat;\n};\n\nexport type SetStringOptions = {\n  /**\n   * The input format of the provided string.\n   * Adjusting this option can help other applications interpret copied string properly.\n   *\n   * @default StringFormat.PLAIN_TEXT\n   */\n  inputFormat?: StringFormat;\n};\n\nexport type AcceptedContentType = 'plain-text' | 'image' | 'url' | 'html';\n\nexport type CornerStyleType = 'dynamic' | 'fixed' | 'capsule' | 'large' | 'medium' | 'small';\n\nexport type DisplayModeType = 'iconAndLabel' | 'iconOnly' | 'labelOnly';\n\nexport type PasteEventPayload = TextPasteEvent | ImagePasteEvent;\n\nexport type TextPasteEvent = {\n  text: string;\n  type: 'text';\n};\n\nexport type ImagePasteEvent = {\n  type: 'image';\n} & ClipboardImage;\n"]}