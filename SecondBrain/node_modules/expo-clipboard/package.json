{"name": "expo-clipboard", "version": "8.0.6", "description": "Provides an interface for getting and setting Clipboard content on Android, iOS and Web.", "main": "build/Clipboard.js", "types": "build/Clipboard.d.ts", "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "expo-clipboard"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-clipboard"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/clipboard", "dependencies": {}, "devDependencies": {"expo-module-scripts": "^5.0.6"}, "peerDependencies": {"expo": "*", "react": "*", "react-native": "*"}, "jest": {"preset": "expo-module-scripts/universal"}, "gitHead": "d635404a12ea7996b987ce0fb7679a238ebcf31b"}