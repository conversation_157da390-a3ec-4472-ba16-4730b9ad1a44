<p>
  <a href="https://docs.expo.dev/versions/latest/sdk/clipboard/">
    <img
      src="../../.github/resources/expo-clipboard.svg"
      alt="expo-clipboard"
      height="64" />
  </a>
</p>

`expo-clipboard` provides an interface for getting and setting Clipboard content on Android, iOS, and Web.

## API documentation

Please refer to the [API documentation for the latest stable release](https://docs.expo.dev/versions/latest/sdk/clipboard/).


## Installation in bare React Native projects

For bare React Native projects, ensure you've [installed and configured the `expo` package](https://docs.expo.dev/bare/installing-expo-modules/).

### Add the package to your npm dependencies

```
npx expo install expo-clipboard
```

### Configure for iOS

Run `npx pod-install` after installing the library.

### Configure for Android

No additional set up necessary.

## Contributing

Contributions are very welcome! Please refer to guidelines described in the [contributing guide](https://github.com/expo/expo#contributing).
