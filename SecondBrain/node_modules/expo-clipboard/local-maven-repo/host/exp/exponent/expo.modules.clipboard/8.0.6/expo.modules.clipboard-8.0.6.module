{"formatVersion": "1.1", "component": {"group": "host.exp.exponent", "module": "expo.modules.clipboard", "version": "8.0.6", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.14.3"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "files": [{"name": "expo.modules.clipboard-8.0.6.aar", "url": "expo.modules.clipboard-8.0.6.aar", "size": 72370, "sha512": "f6483ce9e96a1971ba368b0a1f6a62b205e2e6419b75f5775275854e3ee2d5ebcd07a4abaaf3fe5334020269e20932f9b724d01326f44e39a1bb1318e5bf1082", "sha256": "55abb3dd4e56502cc307530b1407ab54d047075296c579c9a6cf0e48f417af9a", "sha1": "b3fd63279a9f4a077f9ad36a7667de1ef6a6c706", "md5": "5c434100f40b7c9a392fe5686d5a1a3a"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7", "version": {"requires": "2.1.20"}}, {"group": "androidx.core", "module": "core-ktx", "version": {"requires": "1.6.0"}}, {"group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core", "version": {"requires": "1.5.1"}}, {"group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-android", "version": {"requires": "1.5.2"}}], "files": [{"name": "expo.modules.clipboard-8.0.6.aar", "url": "expo.modules.clipboard-8.0.6.aar", "size": 72370, "sha512": "f6483ce9e96a1971ba368b0a1f6a62b205e2e6419b75f5775275854e3ee2d5ebcd07a4abaaf3fe5334020269e20932f9b724d01326f44e39a1bb1318e5bf1082", "sha256": "55abb3dd4e56502cc307530b1407ab54d047075296c579c9a6cf0e48f417af9a", "sha1": "b3fd63279a9f4a077f9ad36a7667de1ef6a6c706", "md5": "5c434100f40b7c9a392fe5686d5a1a3a"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "expo.modules.clipboard-8.0.6-sources.jar", "url": "expo.modules.clipboard-8.0.6-sources.jar", "size": 11576, "sha512": "6591533b2ca78e1dc3efeee53b044232caa7fa724ade2dc7cbe2e7762b5762cbd907ca92b2b25b9e562f1c9930b8ca54efa9b1f02b998c212fdf80ddf9e47344", "sha256": "96bbc0e6a2e01e9a3872bf662def91538c601d7973a072053734416c83f27319", "sha1": "b007009958ebbf8bd4d537422a6fa62e8f840050", "md5": "a4bced5db6a5483d48d45e58c090a52c"}]}]}