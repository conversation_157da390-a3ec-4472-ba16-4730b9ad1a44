/**
 * Automatically generated by expo-modules-test-core.
 *
 * This autogenerated file provides a mock for native Expo module,
 * and works out of the box with the expo jest preset.
 *  */

export type GetStringOptions = any;

export type SetStringOptions = any;

export type URL = any;

export type GetImageOptions = any;

export type UIColor = any;

export type AcceptedTypes = any;

export type CornerStyle = any;

export type DisplayMode = any;

export async function getStringAsync(options: GetStringOptions): Promise<string> {
  return '';
}

export async function setStringAsync(
  content: string | undefined,
  options: SetStringOptions
): Promise<boolean> {
  return false;
}

export async function hasStringAsync(): Promise<boolean> {
  return false;
}

export async function getUrlAsync(): Promise<string | undefined> {
  return '';
}

export async function setUrlAsync(url: URL): Promise<any> {}

export async function hasUrlAsync(): Promise<boolean> {
  return false;
}

export async function setImageAsync(content: string): Promise<any> {}

export async function hasImageAsync(): Promise<boolean> {
  return false;
}

export async function getImageAsync(options: GetImageOptions): Promise<
  | {
      [key: string]: any;
    }
  | undefined
> {
  return {};
}

export type ViewProps = {
  backgroundColor: UIColor | undefined;
  foregroundColor: UIColor | undefined;
  acceptedContentTypes: AcceptedTypes[] | undefined;
  cornerStyle: CornerStyle | undefined;
  displayMode: DisplayMode | undefined;
  imageOptions: GetImageOptions | undefined;
  onPastePressed: (event: any) => void;
};

export function View(props: ViewProps) {}
