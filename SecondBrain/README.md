# SecondBrain - LinkedIn Content Generator

A React Native app that transforms your personal notes into engaging LinkedIn posts using AI.

## Features

### 📝 Notes Management
- **Rich Text Editor**: Markdown-supported note editor with formatting toolbar
- **Folder Organization**: Notion-style folder structure for organizing notes
- **Search & Filter**: Find notes quickly with search functionality
- **Tags Support**: Tag your notes for better organization

### 🤖 AI-Powered Post Generation
- **Smart Content Analysis**: Scans all your notes to understand your thoughts and ideas
- **Topic Tracking**: Remembers previously generated topics to avoid repetition
- **Multiple Tone Options**: Professional, Casual, Inspirational, Educational, Storytelling, Thought Leadership
- **Batch Generation**: Generate 1-5 posts at once
- **Hashtag Suggestions**: Automatically suggests relevant hashtags

### 📱 LinkedIn Integration
- **Post Preview**: Review generated posts before publishing
- **Copy & Share**: Easy copy-to-clipboard and sharing functionality
- **Auto-posting**: (Planned) Direct posting to LinkedIn
- **Post History**: Track what you've posted and when

### 🔔 Smart Notifications
- **Daily Reminders**: Customizable daily notifications to create content
- **Post Reminders**: Gentle nudges to actually post your generated content
- **Scheduling**: Set your preferred notification times

## Tech Stack

- **Frontend**: React Native with Expo
- **Navigation**: React Navigation (Bottom Tabs + Stack)
- **Database**: SQLite (expo-sqlite)
- **AI**: OpenAI GPT-4 API
- **Notifications**: Expo Notifications
- **Storage**: Local file system + SQLite

## Setup Instructions

### Prerequisites
- Node.js (v16 or higher)
- Expo CLI
- OpenAI API key

### Installation

1. **Clone and Install**
   ```bash
   cd SecondBrain
   npm install
   ```

2. **Start the Development Server**
   ```bash
   npm start
   ```

3. **Run on Device/Simulator**
   - Scan QR code with Expo Go app (mobile)
   - Press `i` for iOS simulator
   - Press `a` for Android emulator
   - Press `w` for web browser

### Configuration

1. **OpenAI API Key**
   - Go to Settings tab in the app
   - Enter your OpenAI API key
   - Get your key from: https://platform.openai.com/api-keys

2. **LinkedIn Credentials** (Optional)
   - Enter your LinkedIn username/email and password
   - Note: OAuth integration recommended for production

3. **Notifications**
   - Set your preferred daily reminder time
   - Test notifications to ensure they work

## Usage Workflow

1. **Create Notes**
   - Go to Notes tab
   - Create folders to organize your thoughts
   - Write notes about your ideas, experiences, learnings
   - Use markdown for formatting
   - Add tags for better organization

2. **Generate Posts**
   - Go to Generate tab
   - Review your content stats
   - Choose tone and number of posts
   - Click "Generate Posts"
   - AI will analyze your notes and create LinkedIn-ready content

3. **Review & Post**
   - View generated posts in the Generated Posts screen
   - Copy content to clipboard
   - Share or post directly to LinkedIn
   - Track what you've posted

4. **Daily Routine**
   - Receive daily notifications
   - Add new thoughts to your notes
   - Generate fresh content regularly
   - Build consistent LinkedIn presence

## Key Features Explained

### Smart Topic Avoidance
The app uses semantic similarity to avoid repeating topics you've already posted about, ensuring your content stays fresh and diverse.

### Tone Customization
Choose from different writing tones:
- **Professional**: Formal, business-focused
- **Casual**: Friendly, conversational
- **Inspirational**: Motivating, uplifting
- **Educational**: Teaching-focused, informative
- **Storytelling**: Narrative-driven, personal
- **Thought Leadership**: Insightful, forward-thinking

### Folder Organization
Organize your notes in a hierarchical folder structure similar to Notion, making it easy to categorize different types of thoughts and ideas.

## Development Notes

### Database Schema
- `notes`: Store user notes with markdown content
- `generated_posts`: Track all generated LinkedIn posts
- `folders`: Hierarchical folder structure
- `settings`: App configuration and API keys

### AI Integration
- Uses OpenAI GPT-4 for content generation
- Implements semantic similarity checking
- Maintains context about previously used topics
- Supports multiple output formats and tones

### Notification System
- Local notifications for daily reminders
- Background scheduling support
- Customizable timing and frequency

## Future Enhancements

- [ ] LinkedIn OAuth integration
- [ ] Advanced analytics and insights
- [ ] Content calendar and scheduling
- [ ] Team collaboration features
- [ ] Export/import functionality
- [ ] Cloud sync and backup
- [ ] Advanced AI models and customization
- [ ] Integration with other social platforms

## Contributing

This is a personal productivity tool, but contributions and suggestions are welcome!

## License

MIT License - feel free to use and modify for your own needs.
