import { Linking, Alert } from 'react-native';
import DatabaseService from '../storage/database';

class LinkedInService {
  constructor() {
    this.accessToken = null;
    this.personId = null;
  }

  async initialize() {
    // For now, we'll use a simple approach
    // In a production app, you'd implement proper OAuth flow
    this.username = await DatabaseService.getSetting('linkedin_username');
    this.password = await DatabaseService.getSetting('linkedin_password');
  }

  async setCredentials(username, password) {
    this.username = username;
    this.password = password;
    await DatabaseService.setSetting('linkedin_username', username);
    await DatabaseService.setSetting('linkedin_password', password);
  }

  // Note: This is a simplified implementation
  // In a real app, you would need to implement proper LinkedIn OAuth
  async authenticateWithLinkedIn() {
    try {
      // This is a placeholder for LinkedIn OAuth implementation
      // You would need to:
      // 1. Register your app with LinkedIn
      // 2. Implement OAuth 2.0 flow
      // 3. Get access token and person ID
      
      console.log('LinkedIn authentication would happen here');
      
      // For now, return a mock success
      return {
        success: false,
        message: 'LinkedIn OAuth not implemented yet. This is a placeholder for future implementation.'
      };
    } catch (error) {
      console.error('LinkedIn authentication error:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  async postToLinkedIn(content, hashtags = []) {
    try {
      if (!this.accessToken) {
        throw new Error('Not authenticated with LinkedIn');
      }

      // Format the post content
      const formattedContent = this.formatPostContent(content, hashtags);

      // LinkedIn API endpoint for creating posts
      const response = await fetch('https://api.linkedin.com/v2/ugcPosts', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json',
          'X-Restli-Protocol-Version': '2.0.0'
        },
        body: JSON.stringify({
          author: `urn:li:person:${this.personId}`,
          lifecycleState: 'PUBLISHED',
          specificContent: {
            'com.linkedin.ugc.ShareContent': {
              shareCommentary: {
                text: formattedContent
              },
              shareMediaCategory: 'NONE'
            }
          },
          visibility: {
            'com.linkedin.ugc.MemberNetworkVisibility': 'PUBLIC'
          }
        })
      });

      if (!response.ok) {
        throw new Error(`LinkedIn API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      return {
        success: true,
        postId: result.id,
        message: 'Post published successfully to LinkedIn'
      };

    } catch (error) {
      console.error('Error posting to LinkedIn:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  formatPostContent(content, hashtags) {
    let formattedContent = content;

    // Add hashtags at the end if they're not already in the content
    if (hashtags && hashtags.length > 0) {
      const hashtagString = hashtags.map(tag =>
        tag.startsWith('#') ? tag : `#${tag}`
      ).join(' ');

      // Check if hashtags are already in the content
      const hasHashtags = hashtags.some(tag =>
        content.includes(`#${tag}`) || content.includes(tag)
      );

      if (!hasHashtags) {
        formattedContent += `\n\n${hashtagString}`;
      }
    }

    return formattedContent;
  }

  // Check if LinkedIn app is installed
  async isLinkedInAppInstalled() {
    try {
      return await Linking.canOpenURL('linkedin://');
    } catch (error) {
      return false;
    }
  }

  // Get the best LinkedIn sharing method
  async getBestSharingMethod() {
    const hasApp = await this.isLinkedInAppInstalled();
    return hasApp ? 'app' : 'web';
  }

  // Open LinkedIn app with prefilled content
  async openLinkedInWithContent(content, hashtags = []) {
    try {
      const formattedContent = this.formatPostContent(content, hashtags);

      // Try LinkedIn mobile app deep link first
      const linkedinAppUrl = `linkedin://sharing?text=${encodeURIComponent(formattedContent)}`;

      const canOpenApp = await Linking.canOpenURL(linkedinAppUrl);

      if (canOpenApp) {
        await Linking.openURL(linkedinAppUrl);
        return {
          success: true,
          method: 'app',
          message: 'Opened LinkedIn app with prefilled content'
        };
      } else {
        // Fallback to web LinkedIn sharing
        const webUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent('https://linkedin.com')}&summary=${encodeURIComponent(formattedContent)}`;

        await Linking.openURL(webUrl);
        return {
          success: true,
          method: 'web',
          message: 'Opened LinkedIn web with prefilled content'
        };
      }
    } catch (error) {
      console.error('Error opening LinkedIn:', error);
      return {
        success: false,
        message: 'Failed to open LinkedIn. Please copy the content and post manually.'
      };
    }
  }

  // Enhanced share functionality
  async shareToLinkedIn(content, hashtags = []) {
    try {
      const formattedContent = this.formatPostContent(content, hashtags);

      // LinkedIn sharing URL with proper parameters
      const shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent('https://example.com')}&title=${encodeURIComponent('My LinkedIn Post')}&summary=${encodeURIComponent(formattedContent)}`;

      const canOpen = await Linking.canOpenURL(shareUrl);

      if (canOpen) {
        await Linking.openURL(shareUrl);
        return {
          success: true,
          message: 'LinkedIn sharing opened successfully'
        };
      } else {
        throw new Error('Cannot open LinkedIn sharing URL');
      }
    } catch (error) {
      console.error('Error sharing to LinkedIn:', error);

      // Fallback: try to open LinkedIn app directly
      try {
        const linkedinApp = 'linkedin://';
        const canOpenApp = await Linking.canOpenURL(linkedinApp);

        if (canOpenApp) {
          await Linking.openURL(linkedinApp);
          Alert.alert(
            'LinkedIn Opened',
            'LinkedIn app opened. Please create a new post and paste your content.',
            [{ text: 'OK' }]
          );
          return {
            success: true,
            message: 'LinkedIn app opened - please paste content manually'
          };
        }
      } catch (appError) {
        console.error('Error opening LinkedIn app:', appError);
      }

      return {
        success: false,
        message: 'Could not open LinkedIn. Please install the LinkedIn app or copy content manually.'
      };
    }
  }

  // Simulate posting for development/testing
  async simulatePost(content, hashtags = []) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const formattedContent = this.formatPostContent(content, hashtags);
        console.log('Simulated LinkedIn Post:');
        console.log('------------------------');
        console.log(formattedContent);
        console.log('------------------------');

        resolve({
          success: true,
          postId: `sim_${Date.now()}`,
          message: 'Post simulated successfully (development mode)'
        });
      }, 1000);
    });
  }

  async getPostAnalytics(postId) {
    try {
      if (!this.accessToken) {
        throw new Error('Not authenticated with LinkedIn');
      }

      // LinkedIn API endpoint for post analytics
      const response = await fetch(`https://api.linkedin.com/v2/socialActions/${postId}`, {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'X-Restli-Protocol-Version': '2.0.0'
        }
      });

      if (!response.ok) {
        throw new Error(`LinkedIn API error: ${response.status} ${response.statusText}`);
      }

      const analytics = await response.json();
      
      return {
        success: true,
        data: {
          likes: analytics.numLikes || 0,
          comments: analytics.numComments || 0,
          shares: analytics.numShares || 0,
          views: analytics.numViews || 0
        }
      };

    } catch (error) {
      console.error('Error fetching post analytics:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  // Check if user is authenticated
  isAuthenticated() {
    return !!this.accessToken;
  }

  // Logout
  async logout() {
    this.accessToken = null;
    this.personId = null;
    await DatabaseService.setSetting('linkedin_username', '');
    await DatabaseService.setSetting('linkedin_password', '');
  }

  // Get user profile info
  async getUserProfile() {
    try {
      if (!this.accessToken) {
        throw new Error('Not authenticated with LinkedIn');
      }

      const response = await fetch('https://api.linkedin.com/v2/people/(id~)', {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'X-Restli-Protocol-Version': '2.0.0'
        }
      });

      if (!response.ok) {
        throw new Error(`LinkedIn API error: ${response.status} ${response.statusText}`);
      }

      const profile = await response.json();
      
      return {
        success: true,
        data: {
          id: profile.id,
          firstName: profile.localizedFirstName,
          lastName: profile.localizedLastName,
          headline: profile.localizedHeadline
        }
      };

    } catch (error) {
      console.error('Error fetching user profile:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }
}

export default new LinkedInService();
