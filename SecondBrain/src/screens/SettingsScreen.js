import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DatabaseService from '../storage/database';
import OpenAIService from '../services/openaiService';
import NotificationService from '../services/notificationService';

export default function SettingsScreen() {
  const [openaiApiKey, setOpenaiApiKey] = useState('');
  const [linkedinUsername, setLinkedinUsername] = useState('');
  const [linkedinPassword, setLinkedinPassword] = useState('');
  const [notificationTime, setNotificationTime] = useState('09:00');
  const [autoPostEnabled, setAutoPostEnabled] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showPasswords, setShowPasswords] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const apiKey = await DatabaseService.getSetting('openai_api_key') || '';
      const username = await DatabaseService.getSetting('linkedin_username') || '';
      const password = await DatabaseService.getSetting('linkedin_password') || '';
      const time = await DatabaseService.getSetting('notification_time') || '09:00';
      const autoPost = await DatabaseService.getSetting('auto_post_enabled') === 'true';

      setOpenaiApiKey(apiKey);
      setLinkedinUsername(username);
      setLinkedinPassword(password);
      setNotificationTime(time);
      setAutoPostEnabled(autoPost);
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const saveSettings = async () => {
    try {
      setLoading(true);

      // Save OpenAI API key
      await DatabaseService.setSetting('openai_api_key', openaiApiKey);
      await OpenAIService.setApiKey(openaiApiKey);

      // Save LinkedIn credentials
      await DatabaseService.setSetting('linkedin_username', linkedinUsername);
      await DatabaseService.setSetting('linkedin_password', linkedinPassword);

      // Save notification settings
      await DatabaseService.setSetting('notification_time', notificationTime);
      await NotificationService.updateNotificationTime(notificationTime);

      // Save auto-post setting
      await DatabaseService.setSetting('auto_post_enabled', autoPostEnabled.toString());

      Alert.alert('Success', 'Settings saved successfully!');
    } catch (error) {
      console.error('Error saving settings:', error);
      Alert.alert('Error', 'Failed to save settings');
    } finally {
      setLoading(false);
    }
  };

  const testNotification = async () => {
    try {
      await NotificationService.sendTestNotification();
      Alert.alert('Test Sent', 'Check your notifications!');
    } catch (error) {
      console.error('Error sending test notification:', error);
      Alert.alert('Error', 'Failed to send test notification');
    }
  };

  const clearData = () => {
    Alert.alert(
      'Clear All Data',
      'This will delete all notes, generated posts, and settings. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: async () => {
            try {
              // This would require additional database methods
              Alert.alert('Info', 'Clear data functionality will be implemented');
            } catch (error) {
              console.error('Error clearing data:', error);
              Alert.alert('Error', 'Failed to clear data');
            }
          },
        },
      ]
    );
  };

  const renderSection = (title, children) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {children}
    </View>
  );

  const renderInputField = (label, value, onChangeText, placeholder, secureTextEntry = false) => (
    <View style={styles.inputContainer}>
      <Text style={styles.inputLabel}>{label}</Text>
      <View style={styles.inputWrapper}>
        <TextInput
          style={styles.textInput}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          secureTextEntry={secureTextEntry && !showPasswords}
          autoCapitalize="none"
          autoCorrect={false}
        />
        {secureTextEntry && (
          <TouchableOpacity
            style={styles.eyeButton}
            onPress={() => setShowPasswords(!showPasswords)}
          >
            <Ionicons
              name={showPasswords ? 'eye-off' : 'eye'}
              size={20}
              color="#666"
            />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const renderSwitchField = (label, value, onValueChange, description) => (
    <View style={styles.switchContainer}>
      <View style={styles.switchLeft}>
        <Text style={styles.switchLabel}>{label}</Text>
        {description && <Text style={styles.switchDescription}>{description}</Text>}
      </View>
      <Switch
        value={value}
        onValueChange={onValueChange}
        trackColor={{ false: '#ccc', true: '#0077B5' }}
        thumbColor={value ? '#fff' : '#f4f3f4'}
      />
    </View>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* OpenAI Configuration */}
      {renderSection('OpenAI Configuration', (
        <>
          {renderInputField(
            'API Key',
            openaiApiKey,
            setOpenaiApiKey,
            'Enter your OpenAI API key',
            true
          )}
          <Text style={styles.helpText}>
            Get your API key from platform.openai.com/api-keys
          </Text>
        </>
      ))}

      {/* LinkedIn Configuration */}
      {renderSection('LinkedIn Configuration', (
        <>
          {renderInputField(
            'Username/Email',
            linkedinUsername,
            setLinkedinUsername,
            'Enter your LinkedIn username or email'
          )}
          {renderInputField(
            'Password',
            linkedinPassword,
            setLinkedinPassword,
            'Enter your LinkedIn password',
            true
          )}
          <Text style={styles.helpText}>
            Note: LinkedIn OAuth integration is recommended for production use
          </Text>
        </>
      ))}

      {/* Notification Settings */}
      {renderSection('Notifications', (
        <>
          {renderInputField(
            'Daily Reminder Time',
            notificationTime,
            setNotificationTime,
             'HH:MM (24-hour format)'
          )}
          <TouchableOpacity style={styles.testButton} onPress={testNotification}>
            <Ionicons name="notifications-outline" size={20} color="#0077B5" />
            <Text style={styles.testButtonText}>Test Notification</Text>
          </TouchableOpacity>
        </>
      ))}

      {/* Auto-posting */}
      {renderSection('Auto-posting', (
        <>
          {renderSwitchField(
            'Auto-post to LinkedIn',
            autoPostEnabled,
            setAutoPostEnabled,
            'Automatically post generated content to LinkedIn'
          )}
          <Text style={styles.helpText}>
            When enabled, posts will be automatically published to your LinkedIn profile
          </Text>
        </>
      ))}

      {/* Actions */}
      {renderSection('Actions', (
        <>
          <TouchableOpacity style={styles.actionButton} onPress={saveSettings} disabled={loading}>
            <Ionicons name="checkmark-circle-outline" size={20} color="#4CAF50" />
            <Text style={styles.actionButtonText}>
              {loading ? 'Saving...' : 'Save Settings'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.actionButton, styles.dangerButton]} onPress={clearData}>
            <Ionicons name="trash-outline" size={20} color="#f44336" />
            <Text style={[styles.actionButtonText, styles.dangerText]}>Clear All Data</Text>
          </TouchableOpacity>
        </>
      ))}

      {/* App Info */}
      {renderSection('About', (
        <View style={styles.aboutContainer}>
          <Text style={styles.aboutText}>SecondBrain v1.0.0</Text>
          <Text style={styles.aboutText}>Transform your notes into LinkedIn content</Text>
        </View>
      ))}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  section: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 12,
    padding: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  textInput: {
    flex: 1,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333',
  },
  eyeButton: {
    padding: 12,
  },
  helpText: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
    marginTop: 4,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  switchLeft: {
    flex: 1,
    marginRight: 16,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  switchDescription: {
    fontSize: 12,
    color: '#666',
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E7F3FF',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  testButtonText: {
    color: '#0077B5',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E8F5E8',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  actionButtonText: {
    color: '#4CAF50',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  dangerButton: {
    backgroundColor: '#FFEBEE',
  },
  dangerText: {
    color: '#f44336',
  },
  aboutContainer: {
    alignItems: 'center',
    paddingVertical: 8,
  },
  aboutText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 4,
  },
});
