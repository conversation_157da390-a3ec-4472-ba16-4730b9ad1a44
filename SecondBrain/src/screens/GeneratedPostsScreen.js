import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Share,
  RefreshControl,
} from 'react-native';
import * as Clipboard from 'expo-clipboard';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import DatabaseService from '../storage/database';
import LinkedInService from '../services/linkedinService';

export default function GeneratedPostsScreen({ navigation }) {
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useFocusEffect(
    useCallback(() => {
      loadPosts();
    }, [])
  );

  const loadPosts = async () => {
    try {
      setLoading(true);
      const postsData = await DatabaseService.getGeneratedPosts();
      setPosts(postsData);
    } catch (error) {
      console.error('Error loading posts:', error);
      Alert.alert('Error', 'Failed to load generated posts');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadPosts();
    setRefreshing(false);
  };

  const copyToClipboard = async (content) => {
    try {
      await Clipboard.setStringAsync(content);
      Alert.alert('Copied!', 'Post content copied to clipboard');
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      Alert.alert('Error', 'Failed to copy content');
    }
  };

  const sharePost = async (content) => {
    try {
      await Share.share({
        message: content,
        title: 'LinkedIn Post',
      });
    } catch (error) {
      console.error('Error sharing post:', error);
    }
  };

  const postToLinkedIn = async (post) => {
    Alert.alert(
      'Post to LinkedIn',
      'This will post the content to your LinkedIn profile. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Post',
          onPress: async () => {
            try {
              const hashtags = post.hashtags.split(', ').filter(tag => tag.trim());
              const result = await LinkedInService.simulatePost(post.content, hashtags);
              
              if (result.success) {
                await DatabaseService.markPostAsPosted(post.id);
                Alert.alert('Success!', result.message);
                loadPosts(); // Refresh the list
              } else {
                Alert.alert('Error', result.message);
              }
            } catch (error) {
              console.error('Error posting to LinkedIn:', error);
              Alert.alert('Error', 'Failed to post to LinkedIn');
            }
          },
        },
      ]
    );
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderPostItem = ({ item }) => (
    <View style={styles.postItem}>
      {/* Post Header */}
      <View style={styles.postHeader}>
        <View style={styles.postInfo}>
          <Text style={styles.postDate}>{formatDate(item.created_at)}</Text>
          <Text style={styles.postTone}>Tone: {item.tone}</Text>
        </View>
        <View style={styles.postStatus}>
          {item.is_posted ? (
            <View style={styles.postedBadge}>
              <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
              <Text style={styles.postedText}>Posted</Text>
            </View>
          ) : (
            <View style={styles.draftBadge}>
              <Ionicons name="create-outline" size={16} color="#FF9800" />
              <Text style={styles.draftText}>Draft</Text>
            </View>
          )}
        </View>
      </View>

      {/* Post Content */}
      <Text style={styles.postContent}>{item.content}</Text>

      {/* Hashtags */}
      {item.hashtags && (
        <Text style={styles.hashtags}>
          {item.hashtags.split(', ').map(tag => `#${tag}`).join(' ')}
        </Text>
      )}

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => copyToClipboard(item.content)}
        >
          <Ionicons name="copy-outline" size={20} color="#666" />
          <Text style={styles.actionButtonText}>Copy</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => sharePost(item.content)}
        >
          <Ionicons name="share-outline" size={20} color="#666" />
          <Text style={styles.actionButtonText}>Share</Text>
        </TouchableOpacity>

        {!item.is_posted && (
          <TouchableOpacity
            style={[styles.actionButton, styles.linkedinButton]}
            onPress={() => postToLinkedIn(item)}
          >
            <Ionicons name="logo-linkedin" size={20} color="#0077B5" />
            <Text style={[styles.actionButtonText, styles.linkedinButtonText]}>
              Post
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="bulb-outline" size={64} color="#ccc" />
      <Text style={styles.emptyStateText}>No posts generated yet</Text>
      <Text style={styles.emptyStateSubtext}>
        Go to the Generate tab to create your first LinkedIn post
      </Text>
      <TouchableOpacity
        style={styles.generateButton}
        onPress={() => navigation.navigate('PostGeneratorHome')}
      >
        <Text style={styles.generateButtonText}>Generate Posts</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={posts}
        renderItem={renderPostItem}
        keyExtractor={(item) => item.id.toString()}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={renderEmptyState}
        contentContainerStyle={posts.length === 0 ? styles.emptyContainer : styles.listContainer}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  listContainer: {
    padding: 16,
  },
  emptyContainer: {
    flex: 1,
  },
  postItem: {
    backgroundColor: '#fff',
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  postHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  postInfo: {
    flex: 1,
  },
  postDate: {
    fontSize: 12,
    color: '#999',
    marginBottom: 2,
  },
  postTone: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
  },
  postStatus: {
    alignItems: 'flex-end',
  },
  postedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E8F5E8',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  postedText: {
    fontSize: 12,
    color: '#4CAF50',
    marginLeft: 4,
    fontWeight: '600',
  },
  draftBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF3E0',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  draftText: {
    fontSize: 12,
    color: '#FF9800',
    marginLeft: 4,
    fontWeight: '600',
  },
  postContent: {
    fontSize: 16,
    lineHeight: 24,
    color: '#333',
    marginBottom: 12,
  },
  hashtags: {
    fontSize: 14,
    color: '#0077B5',
    marginBottom: 16,
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    backgroundColor: '#f8f8f8',
  },
  actionButtonText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 6,
    fontWeight: '500',
  },
  linkedinButton: {
    backgroundColor: '#E7F3FF',
  },
  linkedinButtonText: {
    color: '#0077B5',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyStateText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#999',
    marginTop: 16,
    textAlign: 'center',
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 24,
  },
  generateButton: {
    backgroundColor: '#0077B5',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  generateButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
